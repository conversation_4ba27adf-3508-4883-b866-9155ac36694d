# excel整理、按列名对应添加数据到工作表（优化版v2）
import pandas as pd
import os
import openpyxl
import warnings
from datetime import datetime
import shutil
import time
import re

# 忽略特定的弃用警告
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')
warnings.filterwarnings('ignore', category=DeprecationWarning)

def combine_excel(excel_dir, excel_files):
    all_data = pd.DataFrame()
    for file in excel_files:
        file_path = os.path.join(excel_dir, file)
        
        print(f"正在处理文件: {file}")
        try:
            # 尝试读取Excel文件，自动处理结构
            # 1. 首先，尝试标准读取
            df = None
            if file.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            elif file.endswith('.csv'):
                df = pd.read_csv(file_path)
            
            # 2. 检查是否成功读取了标题行（是否包含所需的列）
            required_columns = ['广告名称', '帐户名称', '展示次数', '点击量（全部）']
            columns_found = [col for col in required_columns if col in df.columns]
            
            # 3. 如果缺少关键列，可能是标题行不在第一行，尝试不同的header位置
            if len(columns_found) < len(required_columns) / 2:  # 如果找到的列少于一半
                print(f"  未找到足够的列名，尝试其他标题行位置...")
                
                # 显示前几行数据以便调试
                print("  前5行数据:")
                print(df.head(5))
                
                # 尝试不同的header位置
                for i in range(1, 5):  # 尝试前5行作为标题
                    if file.endswith('.xlsx'):
                        temp_df = pd.read_excel(file_path, header=i)
                    elif file.endswith('.csv'):
                        temp_df = pd.read_csv(file_path, header=i)
                    
                    temp_columns_found = [col for col in required_columns if col in temp_df.columns]
                    print(f"  尝试header={i}, 找到列: {temp_columns_found}")
                    
                    if len(temp_columns_found) > len(columns_found):
                        df = temp_df
                        columns_found = temp_columns_found
                        print(f"  使用header={i}，找到更多列: {columns_found}")
            
            # 如果找不到广告名称列，尝试从数据中提取
            if '广告名称' not in df.columns and len(df.columns) > 2:
                # 假设第三列是广告名称
                df['广告名称'] = df.iloc[:, 2]
                print(f"  使用第3列作为广告名称")
            
            # 如果找不到帐户名称列，尝试从数据中提取
            if '帐户名称' not in df.columns and len(df.columns) > 3:
                # 假设第四列是帐户名称
                df['帐户名称'] = df.iloc[:, 3]
                print(f"  使用第4列作为帐户名称")
            
            print(f"  读取完成，列名: {df.columns.tolist()}")
            
            # 添加文件名列
            df['文件名'] = file
            all_data = pd.concat([all_data, df], ignore_index=True)
            
        except Exception as e:
            print(f"读取文件 {file} 时出错: {e}")
    
    return all_data

def extract_number(text):
    """从文本中提取数字部分"""
    if not isinstance(text, str):
        return text
    numbers = re.findall(r'\d+', text)
    return ''.join(numbers) if numbers else text

def append_to_ledger_sheet(excel_path, new_data, sheet_name='台账'):
    """
    按列名对应添加数据到工作表，并处理公式复制
    """
    start_time = time.time()
    
    try:
        # 创建备份
        backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = excel_path.replace(".xlsx", f"_backup_{backup_time}.xlsx")
        
        # 复制原文件作为备份
        shutil.copy2(excel_path, backup_path)
        print(f"已创建备份文件：{backup_path}")
        
        # 使用openpyxl加载工作簿
        print("正在加载Excel文件...")
        workbook = openpyxl.load_workbook(excel_path)
        
        # 检查工作表是否存在
        if sheet_name not in workbook.sheetnames:
            print(f"工作表 '{sheet_name}' 不存在，将创建新工作表")
            worksheet = workbook.create_sheet(sheet_name)
            
            # 添加表头
            column_headers = [
                '广告账户', '时间', '广告系列', '广告组', '广告名称', '短剧编号', 
                '展示次数', '千次展示费用 (USD)', '点击量（全部）', '调整决策', '成效',
                '点击注册转化率', '展示注册转化率', '点击率', '单次注册成本', '已花费金额 (USD)'
            ]
            for idx, col_name in enumerate(column_headers, 1):
                worksheet.cell(row=1, column=idx, value=col_name)
            
            # 新工作表没有数据，从第2行开始添加
            start_row = 2
            empty_rows = []
        else:
            worksheet = workbook[sheet_name]
            
            # 读取表头，建立列名到列索引的映射
            headers = []
            column_mapping = {}
            for col in range(1, worksheet.max_column + 1):
                header_value = worksheet.cell(row=1, column=col).value
                if header_value is not None:
                    headers.append(header_value)
                    column_mapping[header_value] = col
            
            print(f"工作表 '{sheet_name}' 包含以下列: {headers}")
            
            # 验证所有需要的列都存在
            required_columns = [
                '广告账户', '时间', '广告系列', '广告组', '广告名称', '短剧编号', 
                '展示次数', '千次展示费用 (USD)', '点击量（全部）', '调整决策', '成效',
                '点击注册转化率', '展示注册转化率', '点击率', '单次注册成本', '已花费金额 (USD)'
            ]
            missing_columns = [col for col in required_columns if col not in column_mapping]
            if missing_columns:
                print(f"警告：以下列在工作表中不存在：{missing_columns}")
                print("将添加缺失的列")
                
                # 添加缺失的列
                current_max_col = worksheet.max_column
                for idx, col_name in enumerate(missing_columns, 1):
                    col_position = current_max_col + idx
                    worksheet.cell(row=1, column=col_position, value=col_name)
                    column_mapping[col_name] = col_position
            
            # 找出最后一个非空行
            print("正在查找最后一个非空行...")
            last_non_empty_row = 1  # 默认为表头行
            time_col = column_mapping.get('时间', 2)
            
            for row in range(worksheet.max_row, 1, -1):
                cell_value = worksheet.cell(row=row, column=time_col).value
                if cell_value is not None and str(cell_value).strip() != '':
                    last_non_empty_row = row
                    break
            
            print(f"找到最后一个非空行：第 {last_non_empty_row} 行")
            start_row = last_non_empty_row + 1
            empty_rows = []
        
        # 处理新数据并添加到工作表
        print("正在添加数据...")
        rows_added = 0
        
        # 需要复制上一行公式的列
        formula_columns = [
            '广告系列', '广告组', '短剧编号', '千次展示费用 (USD)', 
            '点击注册转化率', '展示注册转化率', '点击率', '单次注册成本'
        ]
        
        # 映射数据源列到目标列（根据实际Excel文件的列名更新）
        data_mapping = {
            '时间': '报告结束日期',
            '广告名称': '广告名称',
            '展示次数': '展示次数',
            '点击量（全部）': '点击量（全部）',
            '成效': '单次完成注册费用',
            '已花费金额 (USD)': '已花费金额 (USD)',
            '点击率': '点击率（全部）'
        }
        
        for idx, row in enumerate(new_data.iterrows()):
            row_idx, row_data = row
            row_num = start_row + idx
            
            # 处理广告账户 - 从账户名称提取数字部分
            if '帐户名称' in row_data:  # 注意：实际列名是"帐户名称"而不是"账户名称"
                account_name = row_data['帐户名称']
                account_number = extract_number(account_name)
                worksheet.cell(row=row_num, column=column_mapping['广告账户'], value=account_number)
            
            # 添加数据列
            for target_col, source_col in data_mapping.items():
                if source_col in row_data and target_col in column_mapping:
                    cell_value = row_data[source_col]
                    worksheet.cell(row=row_num, column=column_mapping[target_col], value=cell_value)
            
            # 处理广告系列和广告组（直接映射对应的列）
            if '广告系列名称' in row_data and '广告系列' in column_mapping:
                worksheet.cell(row=row_num, column=column_mapping['广告系列'], value=row_data['广告系列名称'])
                
            if '广告组名称' in row_data and '广告组' in column_mapping:
                worksheet.cell(row=row_num, column=column_mapping['广告组'], value=row_data['广告组名称'])
            
            # 处理公式列 - 复制上一行的公式
            if row_num > 2:  # 第一个数据行没有上一行公式可复制
                for formula_col in formula_columns:
                    # 跳过已经直接填充的列
                    if formula_col in ['广告系列', '广告组'] or formula_col not in column_mapping:
                        continue
                        
                    col_idx = column_mapping[formula_col]
                    # 获取上一行的公式或值
                    prev_cell = worksheet.cell(row=row_num-1, column=col_idx)
                    
                    # 复制公式、格式和值
                    new_cell = worksheet.cell(row=row_num, column=col_idx)
                    if prev_cell.value is not None:
                        try:
                            if prev_cell.data_type == 'f':  # 如果是公式
                                new_cell.value = prev_cell.value
                            else:  # 如果不是公式，直接复制值
                                new_cell.value = prev_cell.value
                            
                            # 复制数字格式
                            new_cell.number_format = prev_cell.number_format
                        except Exception as e:
                            print(f"警告：复制单元格 {row_num}, {col_idx} 时出错: {e}")
            
            rows_added += 1
            
            # 每处理100行输出一次进度
            if rows_added % 100 == 0:
                print(f"已添加 {rows_added}/{len(new_data)} 行数据...")
        
        # 保存工作簿
        print("正在保存工作簿...")
        workbook.save(excel_path)
        
        elapsed_time = time.time() - start_time
        print(f"处理完成！用时 {elapsed_time:.2f} 秒")
        print(f"已成功添加 {rows_added} 行数据到工作表 '{sheet_name}'")
        
        return True
    except Exception as e:
        print(f"添加数据过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def update_unique_ad_name_sheet(excel_path, new_data, sheet_name='唯一广告名称'):
    """
    更新唯一广告名称工作表
    """
    try:
        print(f"正在处理 '{sheet_name}' 工作表...")
        
        # 获取唯一广告名称
        unique_ad_names = set()
        if '广告名称' in new_data.columns:
            unique_ad_names = set(new_data['广告名称'].dropna().unique())
        
        if not unique_ad_names:
            print("未找到任何唯一广告名称，跳过更新")
            return True
        
        print(f"找到 {len(unique_ad_names)} 个唯一广告名称")
        
        # 使用openpyxl加载工作簿
        workbook = openpyxl.load_workbook(excel_path)
        
        # 检查工作表是否存在
        if sheet_name not in workbook.sheetnames:
            print(f"工作表 '{sheet_name}' 不存在，将创建新工作表")
            worksheet = workbook.create_sheet(sheet_name)
            # 添加表头
            worksheet.cell(row=1, column=1, value='唯一广告名称')
            # 添加唯一广告名称
            for idx, ad_name in enumerate(sorted(unique_ad_names), 2):
                worksheet.cell(row=idx, column=1, value=ad_name)
        else:
            worksheet = workbook[sheet_name]
            
            # 读取现有的广告名称
            existing_ad_names = set()
            for row in range(2, worksheet.max_row + 1):
                ad_name = worksheet.cell(row=row, column=1).value
                if ad_name:
                    existing_ad_names.add(ad_name)
            
            # 找出新增的广告名称
            new_ad_names = unique_ad_names - existing_ad_names
            if not new_ad_names:
                print("没有新的唯一广告名称需要添加")
                return True
            
            print(f"将添加 {len(new_ad_names)} 个新的唯一广告名称")
            
            # 添加新的广告名称
            start_row = worksheet.max_row + 1
            for idx, ad_name in enumerate(sorted(new_ad_names), 0):
                worksheet.cell(row=start_row + idx, column=1, value=ad_name)
        
        # 保存工作簿
        workbook.save(excel_path)
        print(f"唯一广告名称工作表已更新")
        return True
    except Exception as e:
        print(f"更新唯一广告名称工作表出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # excel合并集的路径 - origin_data路径
    excel_dir = r'C:\Users\<USER>\Desktop\juadge_data_deal\Data\origin_data'
    # excel合并入的路径 - deal_data下的广告数据对比总表
    excel_concat_path = r"C:\Users\<USER>\Desktop\juadge_data_deal\Data\deal_data\广告数据对比总表.xlsx"
    
    # 确保文件路径包含.xlsx扩展名
    if not excel_concat_path.endswith('.xlsx'):
        excel_concat_path += '.xlsx'
    
    try:
        # 只选择真正的xlsx文件，排除临时文件（以~$开头）
        excel_files = [f for f in os.listdir(excel_dir) if (f.endswith('.xlsx') or f.endswith('.csv')) and not f.startswith('~$')]
        
        if not excel_files:
            print("未找到Excel或CSV文件")
        else:
            print(f"找到 {len(excel_files)} 个Excel/CSV文件: {excel_files}")
            combined_data = combine_excel(excel_dir, excel_files)
            print(f"成功合并数据，共 {len(combined_data)} 行")
            
            if len(combined_data) > 0:
                # 显示实际的列名，以便调试
                print("合并数据的实际列名:", combined_data.columns.tolist())
                
                # 添加数据到唯一广告名称工作表
                if '广告名称' in combined_data.columns:
                    update_unique_ad_name_sheet(excel_concat_path, combined_data)
                else:
                    print("警告: 找不到'广告名称'列，跳过更新唯一广告名称工作表")
                
                # 检查是否有足够的列进行处理
                has_basic_columns = ('广告名称' in combined_data.columns and 
                                    '帐户名称' in combined_data.columns)
                
                if has_basic_columns:
                    # 添加数据到台账工作表
                    append_to_ledger_sheet(excel_concat_path, combined_data)
                else:
                    print("警告: 缺少基本列(广告名称、帐户名称)，无法处理数据")
            else:
                print("没有数据需要添加")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc() 