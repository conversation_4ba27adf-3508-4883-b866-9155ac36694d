# excel整理、按列名对应添加数据到工作表
import pandas as pd
import os
import openpyxl
import warnings
from datetime import datetime
import shutil

# 忽略特定的弃用警告
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')
warnings.filterwarnings('ignore', category=DeprecationWarning)

def combine_excel(excel_dir, excel_files):
    all_data = pd.DataFrame()
    for file in excel_files:
        file_path = os.path.join(excel_dir, file)
        
        # 根据文件扩展名选择不同的读取方法
        if file.endswith('.xlsx'):
            df = pd.read_excel(file_path)
        elif file.endswith('.csv'):
            df = pd.read_csv(file_path)
        
        df['文件名'] = file
        all_data = pd.concat([all_data, df], ignore_index=True)
    
    return all_data

def append_by_column_names(excel_path, new_data, sheet_name='台账'):
    """
    按列名对应添加数据到工作表，确保数据放入正确的列
    """
    # 准备要添加的数据
    data_to_insert = pd.DataFrame({
        '时间': new_data['报告开始日期'],
        '广告名称': new_data['广告名称'],
        '展示次数': new_data['展示次数'],
        '点击量（全部）': new_data['点击量（全部）'],
        '成效': new_data['成效'],
        '已花费金额 (USD)': new_data['已花费金额 (USD)']
    })
    
    try:
        # 创建备份
        backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = excel_path.replace(".xlsx", f"_backup_{backup_time}.xlsx")
        
        # 复制原文件作为备份
        shutil.copy2(excel_path, backup_path)
        print(f"已创建备份文件：{backup_path}")
        
        # 使用openpyxl加载工作簿
        workbook = openpyxl.load_workbook(excel_path)
        
        # 检查工作表是否存在
        if sheet_name not in workbook.sheetnames:
            print(f"工作表 '{sheet_name}' 不存在，将创建新工作表")
            worksheet = workbook.create_sheet(sheet_name)
            
            # 添加表头
            for idx, col_name in enumerate(data_to_insert.columns, 1):
                worksheet.cell(row=1, column=idx, value=col_name)
            
            # 确定列映射（在新工作表中，索引正好对应列名）
            column_mapping = {name: idx for idx, name in enumerate(data_to_insert.columns, 1)}
        else:
            worksheet = workbook[sheet_name]
            
            # 读取表头，建立列名到列索引的映射
            headers = []
            column_mapping = {}
            for col in range(1, worksheet.max_column + 1):
                header_value = worksheet.cell(row=1, column=col).value
                if header_value is not None:
                    headers.append(header_value)
                    column_mapping[header_value] = col
            
            print(f"工作表 '{sheet_name}' 包含以下列: {headers}")
        
        # 验证所有需要的列都存在
        missing_columns = [col for col in data_to_insert.columns if col not in column_mapping]
        if missing_columns:
            print(f"警告：以下列在工作表中不存在：{missing_columns}")
            print("这些列的数据将不会被添加。")
        
        # 找出最后一个非空行
        last_row = 1
        for row in range(worksheet.max_row, 0, -1):
            if worksheet.cell(row=row, column=column_mapping.get('时间', 1)).value is not None:
                last_row = row
                break
        
        # 找出非空行中有空白的行
        empty_rows = []
        for row in range(2, last_row + 1):
            if worksheet.cell(row=row, column=column_mapping.get('时间', 1)).value is None:
                empty_rows.append(row)
        
        print(f"找到 {len(empty_rows)} 个可用的空行")
        
        # 如果有空行可用，使用它们
        rows_added = 0
        if empty_rows:
            for idx, data_row in enumerate(data_to_insert.values):
                if idx < len(empty_rows):  # 如果还有空行可用
                    row_num = empty_rows[idx]
                    for col_idx, col_name in enumerate(data_to_insert.columns):
                        if col_name in column_mapping:
                            target_col = column_mapping[col_name]
                            worksheet.cell(row=row_num, column=target_col, value=data_row[col_idx])
                    rows_added += 1
                else:
                    # 如果空行用完了，从最后一行之后开始添加
                    row_num = last_row + 1 + (idx - len(empty_rows))
                    for col_idx, col_name in enumerate(data_to_insert.columns):
                        if col_name in column_mapping:
                            target_col = column_mapping[col_name]
                            worksheet.cell(row=row_num, column=target_col, value=data_row[col_idx])
                    rows_added += 1
        else:
            # 如果没有找到空行，就从最后一行之后开始添加
            start_row = last_row + 1
            for idx, data_row in enumerate(data_to_insert.values):
                row_num = start_row + idx
                for col_idx, col_name in enumerate(data_to_insert.columns):
                    if col_name in column_mapping:
                        target_col = column_mapping[col_name]
                        worksheet.cell(row=row_num, column=target_col, value=data_row[col_idx])
                rows_added += 1
        
        # 保存工作簿
        workbook.save(excel_path)
        print(f"已成功按列名对应添加 {rows_added} 行数据到工作表 '{sheet_name}'")
        
        return True
    except Exception as e:
        print(f"添加数据过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # excel合并集的路径
    excel_dir = r'C:\Users\<USER>\Desktop\数据\数据'
    # excel合并入的路径
    excel_concat_path = r"C:\Users\<USER>\Desktop\广告数据\data\raw\广告数据对比总表20250628-实时 - 副本.xlsx"
    
    # 确保文件路径包含.xlsx扩展名
    if not excel_concat_path.endswith('.xlsx'):
        excel_concat_path += '.xlsx'
    
    try:
        excel_files = [f for f in os.listdir(excel_dir) if (f.endswith('.xlsx') or f.endswith('.csv'))]
        
        if not excel_files:
            print("未找到Excel或CSV文件")
        else:
            print(f"找到 {len(excel_files)} 个Excel/CSV文件")
            combined_data = combine_excel(excel_dir, excel_files)
            print(f"成功合并数据，共 {len(combined_data)} 行")
            
            if len(combined_data) > 0:
                # 按列名对应添加数据
                print("正在按列名对应添加数据...")
                append_by_column_names(excel_concat_path, combined_data, sheet_name='台账')
            else:
                print("没有数据需要添加")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()