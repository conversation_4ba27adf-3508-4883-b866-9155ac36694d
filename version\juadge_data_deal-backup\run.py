#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Advertisement Data Analysis Platform Launcher
Used for packaging applications as entry point
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def get_base_dir():
    """Get the application base directory, compatible with packaged environments"""
    if getattr(sys, 'frozen', False):
        # If it's a packaged application, use sys._MEIPASS or the current executable directory
        if hasattr(sys, '_MEIPASS'):
            return Path(sys._MEIPASS)
        else:
            return Path(os.path.dirname(sys.executable))
    else:
        # If it's a development environment, use the current script directory
        return Path(__file__).parent.absolute()

def main():
    """Main function, starts the Streamlit application"""
    # Get the application base directory
    base_dir = get_base_dir()
    
    # Set the application path
    app_path = os.path.join(base_dir, "Script", "gui", "analysis_xlsx.py")
    
    # If the app.py can't be found after packaging, try other possible paths
    if not os.path.exists(app_path) and getattr(sys, 'frozen', False):
        possible_paths = [
            os.path.join(base_dir, "app.py"),
            os.path.join(base_dir, "Script", "gui", "app.py"),
            os.path.join(os.path.dirname(base_dir), "Script", "gui", "app.py"),
            os.path.join(base_dir.parent, "Script", "gui", "app.py")
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                app_path = path
                break
    
    # Check if the application file exists
    if not os.path.exists(app_path):
        print(f"Error: Application file not found: {app_path}")
        print("Please make sure you're running this script in the correct directory.")
        print(f"Current directory: {os.getcwd()}")
        print(f"Base directory: {base_dir}")
        input("Press any key to exit...")
        sys.exit(1)
    
    # Set environment variables to ensure Streamlit can find the correct path
    os.environ["PYTHONPATH"] = str(base_dir)
    
    # Setup Streamlit run parameters
    streamlit_cmd = [
        sys.executable,
        "-m",  # Use -m to ensure proper module execution
        "streamlit",
        "run", 
        app_path,
        #"--server.port", "8501",
        "--server.headless", "true",
        "--browser.serverAddress", "localhost",
        "--server.enableCORS", "false",
        "--theme.base", "light"
    ]
    
    print("Starting Advertisement Data Analysis Platform...")
    print(f"Application path: {app_path}")
    
    # Try opening browser
    try:
        # Delay browser opening by 2 seconds to give the server time to start
        def open_browser():
            time.sleep(2)
            #webbrowser.open("http://localhost:8501")
        
        import threading
        threading.Thread(target=open_browser).start()
    except Exception as e:
        print(f"Cannot automatically open browser: {e}")
        #print("Please manually visit: http://localhost:8501")
    
    # Start Streamlit application
    try:
        # Use subprocess to run the Streamlit command
        subprocess.run(streamlit_cmd)
    except KeyboardInterrupt:
        print("\nUser interruption, closing application...")
    except Exception as e:
        print(f"Error starting application: {e}")
        print("\nPlease ensure all dependencies are installed. You can run:")
        print("pip install -r Script/gui/requirements.txt")
        input("Press any key to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main() 