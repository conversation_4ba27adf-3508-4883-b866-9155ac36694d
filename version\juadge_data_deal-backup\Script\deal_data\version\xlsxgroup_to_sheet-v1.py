# excel整理、按列名对应添加数据到工作表（优化版）
import pandas as pd
import os
import openpyxl
import warnings
from datetime import datetime
import shutil
import time

# 忽略特定的弃用警告
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')
warnings.filterwarnings('ignore', category=DeprecationWarning)

def combine_excel(excel_dir, excel_files):
    all_data = pd.DataFrame()
    for file in excel_files:
        file_path = os.path.join(excel_dir, file)
        
        # 根据文件扩展名选择不同的读取方法
        if file.endswith('.xlsx'):
            df = pd.read_excel(file_path)
        elif file.endswith('.csv'):
            df = pd.read_csv(file_path)
        
        df['文件名'] = file
        all_data = pd.concat([all_data, df], ignore_index=True)
    
    return all_data

def append_by_column_names_optimized(excel_path, new_data, sheet_name='台账'):
    """
    按列名对应添加数据到工作表，优化空行处理
    """
    start_time = time.time()
    
    # 准备要添加的数据 - 更新列映射
    data_to_insert = pd.DataFrame({
        '时间': new_data['报告结束日期'],
        '广告名称': new_data['广告名称'],
        '展示次数': new_data['展示次数'],
        '点击量（全部）': new_data['点击量（全部）'],
        '成效': new_data['单次完成注册费用'],
        '已花费金额 (USD)': new_data['已花费金额 (USD)']
    })
    
    try:
        # 创建备份
        backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = excel_path.replace(".xlsx", f"_backup_{backup_time}.xlsx")
        
        # 复制原文件作为备份
        shutil.copy2(excel_path, backup_path)
        print(f"已创建备份文件：{backup_path}")
        
        # 使用openpyxl加载工作簿
        print("正在加载Excel文件...")
        workbook = openpyxl.load_workbook(excel_path)
        
        # 检查工作表是否存在
        if sheet_name not in workbook.sheetnames:
            print(f"工作表 '{sheet_name}' 不存在，将创建新工作表")
            worksheet = workbook.create_sheet(sheet_name)
            
            # 添加表头 - 确保第一列是'唯一广告名称'
            column_headers = ['唯一广告名称', '序号'] + list(data_to_insert.columns)
            for idx, col_name in enumerate(column_headers, 1):
                worksheet.cell(row=1, column=idx, value=col_name)
            
            # 确定列映射
            column_mapping = {name: idx for idx, name in enumerate(column_headers, 1)}
            
            # 新工作表没有数据，从第2行开始添加
            start_row = 2
            empty_rows = []
        else:
            worksheet = workbook[sheet_name]
            
            # 读取表头，建立列名到列索引的映射
            headers = []
            column_mapping = {}
            for col in range(1, worksheet.max_column + 1):
                header_value = worksheet.cell(row=1, column=col).value
                if header_value is not None:
                    headers.append(header_value)
                    column_mapping[header_value] = col
            
            print(f"工作表 '{sheet_name}' 包含以下列: {headers}")
            
            # 验证所有需要的列都存在
            required_columns = ['唯一广告名称', '序号'] + list(data_to_insert.columns)
            missing_columns = [col for col in required_columns if col not in column_mapping]
            if missing_columns:
                print(f"警告：以下列在工作表中不存在：{missing_columns}")
                print("将添加缺失的列")
                
                # 添加缺失的列
                current_max_col = worksheet.max_column
                for idx, col_name in enumerate(missing_columns, 1):
                    col_position = current_max_col + idx
                    worksheet.cell(row=1, column=col_position, value=col_name)
                    column_mapping[col_name] = col_position
            
            # 优化空行查找 - 一次性读取时间列的所有值
            print("正在分析工作表数据...")
            time_column = column_mapping.get('时间', 3)  # 假设时间列在第3列之后
            
            # 创建一个字典，记录每一行的状态（空/非空）
            row_status = {}
            
            # 找出最后一个非空行和所有空行
            last_non_empty_row = 1  # 默认为表头行
            empty_rows = []
            
            # 批量处理，每次处理1000行
            batch_size = 1000
            max_row = worksheet.max_row
            
            for start in range(2, max_row + 1, batch_size):
                end = min(start + batch_size - 1, max_row)
                print(f"正在分析行 {start} 到 {end}...")
                
                for row in range(start, end + 1):
                    cell_value = worksheet.cell(row=row, column=time_column).value
                    if cell_value is None or str(cell_value).strip() == '':
                        empty_rows.append(row)
                        row_status[row] = 'empty'
                    else:
                        last_non_empty_row = row
                        row_status[row] = 'non_empty'
            
            print(f"分析完成：最后一个非空行是第 {last_non_empty_row} 行")
            print(f"找到 {len(empty_rows)} 个空行")
            
            # 如果没有空行，或者空行都在最后一个非空行之后，从最后一行之后开始添加
            if not empty_rows or all(row > last_non_empty_row for row in empty_rows):
                start_row = last_non_empty_row + 1
                empty_rows = []  # 清空空行列表，因为我们不使用它们
            else:
                # 只保留在最后一个非空行之前的空行
                empty_rows = [row for row in empty_rows if row <= last_non_empty_row]
                empty_rows.sort()  # 确保按行号排序
                print(f"将使用 {len(empty_rows)} 个位于数据中间的空行")
        
        # 添加数据
        print("正在添加数据...")
        rows_added = 0
        unique_ad_names = set()  # 用于跟踪唯一广告名称
        
        # 先使用空行
        for idx, data_row in enumerate(data_to_insert.values):
            ad_name = data_row[1]  # 广告名称在data_row中的索引
            unique_ad_names.add(ad_name)
            
            if idx < len(empty_rows):
                # 使用空行
                row_num = empty_rows[idx]
            else:
                # 空行用完，从start_row开始添加剩余数据
                remaining_idx = idx - len(empty_rows)
                row_num = start_row + remaining_idx
            
            # 添加唯一广告名称到第1列
            worksheet.cell(row=row_num, column=column_mapping['唯一广告名称'], value=ad_name)
            
            # 添加序号公式到第2列 (=上一行公式+1，如果是第一个数据行，则为1)
            if row_num == 2:
                worksheet.cell(row=row_num, column=column_mapping['序号'], value=1)
            else:
                # 创建引用上一行的公式
                formula = f"=R{row_num-1}C{column_mapping['序号']}+1"
                worksheet.cell(row=row_num, column=column_mapping['序号'], value=formula).number_format = "0"
            
            # 添加其他数据列
            for col_idx, col_name in enumerate(data_to_insert.columns):
                if col_name in column_mapping:
                    target_col = column_mapping[col_name]
                    worksheet.cell(row=row_num, column=target_col, value=data_row[col_idx])
            
            rows_added += 1
            
            # 每处理100行输出一次进度
            if rows_added % 100 == 0:
                print(f"已添加 {rows_added}/{len(data_to_insert)} 行数据...")
        
        # 保存工作簿
        print("正在保存工作簿...")
        workbook.save(excel_path)
        
        elapsed_time = time.time() - start_time
        print(f"处理完成！用时 {elapsed_time:.2f} 秒")
        print(f"已成功按列名对应添加 {rows_added} 行数据到工作表 '{sheet_name}'")
        print(f"处理了 {len(unique_ad_names)} 个唯一广告名称")
        
        return True
    except Exception as e:
        print(f"添加数据过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # excel合并集的路径 - 更新为origin_data路径
    excel_dir = r'C:\Users\<USER>\Desktop\juadge_data_deal\Data\origin_data'
    # excel合并入的路径 - 更新为deal_data下的广告数据对比总表
    excel_concat_path = r"C:\Users\<USER>\Desktop\juadge_data_deal\Data\deal_data\广告数据对比总表.xlsx"
    
    # 确保文件路径包含.xlsx扩展名
    if not excel_concat_path.endswith('.xlsx'):
        excel_concat_path += '.xlsx'
    
    try:
        # 只选择真正的xlsx文件，排除临时文件（以~$开头）
        excel_files = [f for f in os.listdir(excel_dir) if (f.endswith('.xlsx') or f.endswith('.csv')) and not f.startswith('~$')]
        
        if not excel_files:
            print("未找到Excel或CSV文件")
        else:
            print(f"找到 {len(excel_files)} 个Excel/CSV文件: {excel_files}")
            combined_data = combine_excel(excel_dir, excel_files)
            print(f"成功合并数据，共 {len(combined_data)} 行")
            
            if len(combined_data) > 0:
                # 检查是否有所需的列
                required_columns = ['报告结束日期', '广告名称', '展示次数', '点击量（全部）', '单次完成注册费用', '已花费金额 (USD)']
                missing_columns = [col for col in required_columns if col not in combined_data.columns]
                if missing_columns:
                    print(f"警告：合并的数据缺少以下列：{missing_columns}")
                    print("请确保源数据包含所有需要的列")
                else:
                    # 按列名对应添加数据（优化版）
                    print("正在按列名对应添加数据（优化版）...")
                    append_by_column_names_optimized(excel_concat_path, combined_data, sheet_name='台账')
            else:
                print("没有数据需要添加")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()