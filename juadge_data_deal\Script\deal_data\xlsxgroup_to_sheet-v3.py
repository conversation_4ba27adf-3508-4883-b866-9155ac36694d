# excel整理、按列名对应添加数据到工作表（简化版v3）
import pandas as pd
import os
import openpyxl
import warnings
from datetime import datetime
import shutil
import time
import re
import traceback  # 添加跟踪模块

# 忽略特定的弃用警告
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')
warnings.filterwarnings('ignore', category=DeprecationWarning)

def read_excel_file(file_path):
    """
    尝试以多种方式读取Excel文件，找到最合适的数据结构
    """
    print(f"读取文件: {file_path}")
    
    try:
        # 跳过第一行（汇总行）
        df = pd.read_excel(file_path, skiprows=1)
        print(f"跳过第一行读取成功，列名: {df.columns.tolist()}")
        
        # 打印数据的前几行，便于调试
        print("数据预览:")
        print(df.head(1))
        
        # 检查是否需要特别处理（没有找到有用的列名）
        if all('Unnamed' in str(col) for col in df.columns if isinstance(col, str)):
            print("未检测到有效列名，尝试读取其他行作为标题...")
            # 尝试读取第二行作为标题
            df = pd.read_excel(file_path, header=1, skiprows=1)
            print(f"使用第2行作为标题，列名: {df.columns.tolist()}")
            print(df.head(1))
            
        return df
    except Exception as e:
        print(f"读取文件失败: {e}")
        traceback.print_exc()  # 打印详细错误
        return pd.DataFrame()  # 返回空DataFrame

# 修改extract_number函数，添加参数使其可以返回整数类型
def extract_number(text, as_int=True):
    """
    从文本中提取数字部分
    as_int: 如果为True，则将结果转为整数，去掉前导零
    """
    if not isinstance(text, str):
        if as_int and isinstance(text, (int, float)):
            return int(text)
        return text
    
    numbers = re.findall(r'\d+', text)
    if not numbers:
        return text
    
    # 连接数字字符
    result = ''.join(numbers)
    
    # 尝试转为整数以去掉前导零
    if as_int:
        try:
            return int(result)
        except ValueError:
            return result
    
    return result


# 修改日期格式化函数，去掉前导零
def format_date(date_value):
    """将日期格式化为 yyyy/m/d 格式(没有前导零)"""
    if not date_value:
        return date_value
    
    try:
        if isinstance(date_value, datetime):
            # 使用%-格式化移除前导零（在Windows上可能不起作用，需要特殊处理）
            try:
                return date_value.strftime('%Y/%-m/%-d')  # Linux/Mac格式
            except ValueError:
                # Windows格式处理
                month = str(date_value.month)
                day = str(date_value.day)
                return f"{date_value.year}/{month}/{day}"
        elif isinstance(date_value, str):
            # 尝试解析日期字符串
            date_obj = datetime.strptime(date_value, '%Y-%m-%d')
            try:
                return date_obj.strftime('%Y/%-m/%-d')  # Linux/Mac格式
            except ValueError:
                # Windows格式处理
                month = str(date_obj.month)
                day = str(date_obj.day)
                return f"{date_obj.year}/{month}/{day}"
        return date_value
    except:
        return date_value  # 如果转换失败，返回原值

def ensure_formula_has_equals(formula):
    """确保公式以等号开头"""
    if formula and isinstance(formula, str) and not formula.startswith('='):
        return '=' + formula
    return formula

def find_last_data_row(worksheet, min_check=10):
    """
    查找最后一个非空行，通过检查前几列的实际数据
    min_check: 最小需要检查的列数
    """
    print("查找最后一个实际数据行...")
    
    # 获取工作表的维度
    max_row = worksheet.max_row
    max_col = min(worksheet.max_column, 15)  # 最多检查前15列
    
    # 关键列 - 确保这些列包含实际数据(时间、广告名称、广告账户等)
    key_columns = [2, 5]  # 时间和广告名称列
    
    # 从最后一行开始向上查找有实际数据的行
    for row in range(max_row, 1, -1):
        has_data = False
        for col in range(1, max_col + 1):
            cell = worksheet.cell(row=row, column=col)
            if cell.value is not None and cell.value != "":
                # 如果单元格不是公式或公式已经计算过有值
                if cell.data_type != 'f' or (isinstance(cell.value, (int, float)) and col in key_columns):
                    print(f"在行 {row}, 列 {col} 找到数据: {cell.value}")
                    has_data = True
                    break
        if has_data:
            return row
    
    # 如果没有找到数据行，返回标题行
    return 1

def get_numeric_columns(data, top_n=10):
    """
    分析数据框，找出包含数值的列并按大小排序
    返回按平均值排序的列名列表
    """
    numeric_cols = {}
    
    # 尝试将每一列转换为数值型
    for col in data.columns:
        try:
            # 取平均值，忽略NaN
            values = pd.to_numeric(data[col], errors='coerce')
            if not values.isna().all():  # 如果列中有非NaN值
                mean_val = values.mean()
                if not pd.isna(mean_val):
                    numeric_cols[col] = mean_val
                    print(f"列 {col} 平均值: {mean_val}")
        except:
            pass
    
    # 按值大小排序
    sorted_cols = sorted(numeric_cols.items(), key=lambda x: x[1], reverse=True)
    return sorted_cols[:top_n]

# 修改process_files_to_excel函数，添加排序处理
def process_files_to_excel(source_dir, target_excel, target_sheet='台账'):
    """
    处理源目录中的所有Excel文件，并将数据添加到目标Excel的指定工作表
    改进版：分别处理每个Excel文件，避免合并后列名混乱的问题
    """
    # 1. 获取所有Excel文件
    excel_files = [f for f in os.listdir(source_dir) if f.endswith('.xlsx') and not f.startswith('~$')]
    if not excel_files:
        print("未找到Excel文件!")
        return False
    
    print(f"找到 {len(excel_files)} 个Excel文件: {excel_files}")
    
    # 2. 创建工作簿备份
    backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = target_excel.replace(".xlsx", f"_backup_{backup_time}.xlsx")
    
    if os.path.exists(target_excel):
        try:
            shutil.copy2(target_excel, backup_path)
            print(f"已创建备份文件：{backup_path}")
        except Exception as e:
            print(f"创建备份时出错: {e}")
            print("尝试继续处理...")
    
    # 3. 加载目标工作簿
    print("正在加载目标工作簿...")
    try_count = 0
    while try_count < 3:
        try:
            try_count += 1
            workbook = openpyxl.load_workbook(target_excel)
            print("目标工作簿加载成功")
            break
        except Exception as e:
            if try_count < 3:
                print(f"加载工作簿失败，尝试再次加载: {e}")
                time.sleep(2)  # 等待2秒再试
            else:
                print(f"多次尝试后仍无法加载工作簿: {e}")
                return False
    
    try:
        # 4. 创建或更新唯一广告名称工作表
        # 为此我们需要先读取所有文件的广告名称数据
        all_ad_names = []
        for file in excel_files:
            file_path = os.path.join(source_dir, file)
            df = read_excel_file(file_path)
            if not df.empty:
                # 尝试从不同可能的列名获取广告名称
                possible_ad_name_cols = ['广告名称', '广告', 'Unnamed: 2', 2]
                for col in possible_ad_name_cols:
                    try:
                        if col in df.columns:
                            names = df[col].dropna().unique().tolist()
                            all_ad_names.extend(names)
                            print(f"从文件 {file} 获取了 {len(names)} 个广告名称")
                            break
                    except:
                        continue
        
        # 去重
        all_ad_names = list(set(all_ad_names))
        print(f"总共收集到 {len(all_ad_names)} 个唯一广告名称")
        
        # 更新唯一广告名称工作表
        print("开始处理唯一广告名称工作表...")
        create_unique_ad_name_sheet(workbook, all_ad_names)
        
        # 5. 查找工作表最后一行
        if target_sheet in workbook.sheetnames:
            worksheet = workbook[target_sheet]
            last_row = find_last_data_row(worksheet)
            print(f"台账最后一行数据: {last_row}")
        else:
            # 创建新工作表
            worksheet = workbook.create_sheet(target_sheet)
            
            # 添加表头
            headers = [
                '广告账户', '时间', '广告系列', '广告组', '广告名称', '短剧编号',
                '展示次数', '千次展示费用 (USD)', '点击量（全部）', '调整决策', '成效',
                '点击注册转化率', '展示注册转化率', '点击率', '单次注册成本', '已花费金额 (USD)'
            ]
            for idx, header in enumerate(headers):
                worksheet.cell(row=1, column=idx+1, value=header)
            
            last_row = 1
            print(f"已创建表头: {headers}")
        
        # 6. 收集所有数据，之后一次性排序后添加
        all_processed_data = []
        
        for file in excel_files:
            file_path = os.path.join(source_dir, file)
            print(f"\n开始处理文件: {file}")
            
            df = read_excel_file(file_path)
            if df.empty:
                print(f"文件 {file} 没有有效数据，跳过")
                continue
            
            # 过滤空行
            filtered_data = filter_empty_rows(df)
            print(f"文件 {file} 过滤后数据行数: {len(filtered_data)}")
            
            if len(filtered_data) == 0:
                print(f"文件 {file} 过滤后没有数据，跳过")
                continue
            
            # 分析数值列
            numeric_cols = get_numeric_columns(filtered_data)
            print(f"文件 {file} 数值列排序: {numeric_cols}")
            
            # 为每个文件的数据处理列映射和转换
            processed_data = process_data_for_import(filtered_data, numeric_cols, file)
            if processed_data:
                all_processed_data.extend(processed_data)
                print(f"文件 {file} 处理了 {len(processed_data)} 行数据")
        
        # 按广告账户从小到大排序
        if all_processed_data:
            print("对所有数据按广告账户从小到大排序...")
            # 尝试将广告账户直接用于排序，因为已经在process_data_for_import中处理为整数
            for item in all_processed_data:
                try:
                    if '广告账户' in item:
                        # 如果已经是整数，不需要再转换
                        item['广告账户_排序'] = int(item['广告账户']) if not isinstance(item['广告账户'], int) else item['广告账户']
                    else:
                        item['广告账户_排序'] = 999
                except:
                    item['广告账户_排序'] = 999  # 如果无法转换为数字，则放到最后
            
            # 排序
            all_processed_data.sort(key=lambda x: x['广告账户_排序'])
            print(f"排序后的广告账户顺序: {[item['广告账户'] for item in all_processed_data[:5]]}")
            
            # 处理台账工作表 - 使用智能合并功能
            print("开始智能合并数据（相同记录将更新数值，新记录将添加）...")
            rows_processed = merge_data_to_sheet(workbook, all_processed_data, target_sheet, last_row + 1)
            print(f"总共处理了 {rows_processed} 条数据")
        else:
            print("没有数据需要添加")
        
        # 7. 保存工作簿
        print("正在保存工作簿...")
        try_count = 0
        while try_count < 3:
            try:
                try_count += 1
                workbook.save(target_excel)
                print(f"数据已成功保存到 {target_excel}")
                break
            except PermissionError as e:
                if try_count < 3:
                    print(f"保存失败，文件可能被占用。尝试另存为新文件: {e}")
                    # 尝试另存为新文件
                    new_file = target_excel.replace(".xlsx", f"_new_{backup_time}.xlsx")
                    workbook.save(new_file)
                    print(f"数据已保存到新文件: {new_file}")
                    break
                else:
                    print(f"多次尝试后仍无法保存: {e}")
                    return False
                    
        return True
    
    except Exception as e:
        print(f"处理工作簿时出错: {e}")
        traceback.print_exc()  # 打印详细错误
        return False


def create_unique_ad_name_sheet(workbook, ad_names, sheet_name='唯一广告名称'):
    """
    创建或更新唯一广告名称工作表，并添加短剧编号列
    修改版：直接接收广告名称列表
    """
    try:
        if not ad_names:
            print("没有广告名称数据")
            return
        
        # 检查工作表是否存在
        if sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]
            print(f"找到工作表 '{sheet_name}'")
            
            # 确保有短剧编号列
            if worksheet.max_column < 2 or worksheet.cell(row=1, column=2).value != "短剧编号":
                worksheet.cell(row=1, column=2, value="短剧编号")
                print("添加'短剧编号'列头")
            
            # 读取现有的广告名称
            existing_names = []
            for row in range(2, worksheet.max_row + 1):
                cell_value = worksheet.cell(row=row, column=1).value
                if cell_value:
                    existing_names.append(cell_value)
            
            # 找出新的广告名称
            new_names = [name for name in ad_names if name not in existing_names]
            if new_names:
                start_row = worksheet.max_row + 1
                for idx, name in enumerate(new_names):
                    worksheet.cell(row=start_row + idx, column=1, value=name)
                print(f"已添加 {len(new_names)} 个新的唯一广告名称")
            else:
                print("没有新的广告名称需要添加")
        else:
            # 创建新的工作表
            worksheet = workbook.create_sheet(sheet_name)
            worksheet.cell(row=1, column=1, value='唯一广告名称')
            worksheet.cell(row=1, column=2, value='短剧编号')
            
            for idx, name in enumerate(ad_names):
                worksheet.cell(row=idx + 2, column=1, value=name)
            print(f"已创建唯一广告名称工作表，包含 {len(ad_names)} 个广告名称")
        
        print("唯一广告名称工作表处理完成")
    except Exception as e:
        print(f"处理唯一广告名称工作表时出错: {e}")
        traceback.print_exc()  # 打印详细错误


def update_cell_reference(formula, row_diff):
    """
    更新单元格引用，例如将E977更新为E(977+row_diff)
    """
    # 匹配单元格引用，例如E977
    pattern = r'([A-Z]+)(\d+)'
    
    def replace_ref(match):
        col = match.group(1)  # 列标识，如E
        row = int(match.group(2))  # 行号，如977
        return f"{col}{row + row_diff}"
    
    return re.sub(pattern, replace_ref, formula)

def update_formula_references(formula, current_row):
    """
    更新公式中的行引用，使其引用当前行
    current_row: 当前要插入的行号
    """
    # 确保公式有等号
    formula = ensure_formula_has_equals(formula)
    
    # 处理VLOOKUP公式，例如将VLOOKUP(E1073,...)改为VLOOKUP(E1074,...)
    if "VLOOKUP" in formula:
        # 匹配VLOOKUP的第一个参数中的行引用，如VLOOKUP(E1073,...)中的E1073
        vlookup_pattern = r'(VLOOKUP\()([A-Z]+)(\d+)(,.+)'
        match = re.search(vlookup_pattern, formula)
        if match:
            col = match.group(2)  # 列标识，如E
            # 替换为当前行号
            formula = match.group(1) + col + str(current_row) + match.group(4)
    
    # 处理简单公式，如I1073/G1073等
    simple_patterns = [
        r'([A-Z]+)(\d+)(/[A-Z]+)(\d+)',  # 如 I1073/G1073
        r'([A-Z]+)(\d+)(\*[A-Z]+)(\d+)',  # 如 P1073*G1073
        r'([A-Z]+)(\d+)(\+[A-Z]+)(\d+)',  # 如 P1073+G1073
        r'([A-Z]+)(\d+)(-[A-Z]+)(\d+)'   # 如 P1073-G1073
    ]
    
    for pattern in simple_patterns:
        if re.search(pattern, formula):
            # 使用函数替换所有单元格引用
            def replace_cell_ref(match):
                col = match.group(1)
                return col + str(current_row)
            
            # 替换公式中的所有单元格引用
            formula = re.sub(r'([A-Z]+)\d+', replace_cell_ref, formula)
    
    return formula

# 修改过滤空行函数，让它能够返回更多诊断信息
def filter_empty_rows(data):
    """
    过滤掉空数据行，确保只添加有实际数据的行
    安全处理，检查列是否存在
    """
    # 首先获取实际存在的列
    filtered_data = data
    
    # 查找关键列
    key_columns = []
    for col in ['广告名称', '展示次数', '点击量（全部）', '帐户名称', '账户名称', '报告结束日期', 'Unnamed: 3', 3]:
        if col in data.columns:
            key_columns.append(col)
    
    # 如果找到了关键列，使用它们来过滤空行
    if key_columns:
        print(f"使用以下列过滤空行: {key_columns}")
        filtered_data = data.dropna(subset=key_columns, how='all')
        if len(filtered_data) < len(data):
            print(f"过滤掉 {len(data) - len(filtered_data)} 行空数据，保留 {len(filtered_data)} 行")
    else:
        print("未找到可用于过滤的关键列，将使用所有数据")
    
    # 打印数据框的前几列，便于诊断
    print("过滤后数据预览:")
    try:
        preview = filtered_data.head(2).to_string()
        print(preview)
    except:
        print("无法打印数据预览")
    
    return filtered_data

# 修改处理台账函数中的列映射逻辑
def process_ledger_sheet(workbook, data, sheet_name='台账', numeric_cols=None, start_row=None):
    """
    处理台账工作表 - 增强版
    修改版：接收起始行参数
    """
    try:
        print(f"处理工作表 '{sheet_name}'")
        
        # 检查工作表是否存在
        if sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]
            print(f"找到工作表 '{sheet_name}'")
            
            # 获取表头信息
            headers = []
            column_mapping = {}
            for col in range(1, worksheet.max_column + 1):
                header = worksheet.cell(row=1, column=col).value
                if header:
                    headers.append(header)
                    column_mapping[header] = col
            
            print(f"表头: {headers}")
            
            # 使用传入的起始行或者查找最后一行数据
            if start_row is None:
                last_row = find_last_data_row(worksheet)
                print(f"最后一行数据: {last_row}")
                start_row = last_row + 1
            
        else:
            # 创建新工作表
            worksheet = workbook.create_sheet(sheet_name)
            
            # 添加表头
            headers = [
                '广告账户', '时间', '广告系列', '广告组', '广告名称', '短剧编号',
                '展示次数', '千次展示费用 (USD)', '点击量（全部）', '调整决策', '成效',
                '点击注册转化率', '展示注册转化率', '点击率', '单次注册成本', '已花费金额 (USD)'
            ]
            for idx, header in enumerate(headers):
                worksheet.cell(row=1, column=idx+1, value=header)
            
            column_mapping = {header: idx+1 for idx, header in enumerate(headers)}
            start_row = 2
            print(f"已创建表头: {headers}")
        
        print(f"从第 {start_row} 行开始添加数据")
        
        # 检查数据列
        print("数据列预览:")
        for col in data.columns:
            try:
                preview = data[col].iloc[0]
                print(f"列 {col}: {preview}")
            except:
                print(f"列 {col}: 无法获取预览")
        
        # 创建列映射
        column_mappings = {}
        
        # 1. 尝试通过列名映射
        column_names_to_find = {
            '广告账户': ['帐户名称', '账户名称', '账户', 'Unnamed: 3', 3],
            '时间': ['报告结束日期', '报告日期', '日期', 'Unnamed: 13', 13, '2025-07-17', '2025-07-17.1', '2025-07-31', '2025-07-31.1'],
            '广告名称': ['广告名称', '广告', 'Unnamed: 2', 2],
            '展示次数': ['展示次数', '展示量', 'Unnamed: 5', 5, 20158, 38870],
            '点击量（全部）': ['点击量（全部）', '点击量', 'Unnamed: 10', 10, 1755, 4980],
            '成效': ['网站完成注册次数', '网站注册费用', '成效', 'Unnamed: 9', 9, 58, 110, 0.43844828],
            '已花费金额 (USD)': ['已花费金额 (USD)', '已花费金额', '花费', 'Unnamed: 7', 7, 25.43, 50.19, 'USD']
        }
        
        for target_col, possible_cols in column_names_to_find.items():
            for col in possible_cols:
                if col in data.columns:
                    column_mappings[target_col] = col
                    print(f"通过列名匹配: '{target_col}' -> '{col}'")
                    break
        
        # 2. 使用数值列信息来猜测
        if numeric_cols and len(numeric_cols) > 0:
            # 按值从大到小排序
            for i, (col, val) in enumerate(numeric_cols):
                if '展示次数' not in column_mappings and i == 0:
                    column_mappings['展示次数'] = col
                    print(f"通过数值大小猜测: '展示次数' -> '{col}' (最大值: {val})")
                elif '点击量（全部）' not in column_mappings and i == 1:
                    column_mappings['点击量（全部）'] = col
                    print(f"通过数值大小猜测: '点击量（全部）' -> '{col}' (第二大值: {val})")
                elif '已花费金额 (USD)' not in column_mappings and 1 < i < len(numeric_cols) - 1:
                    column_mappings['已花费金额 (USD)'] = col
                    print(f"通过数值大小猜测: '已花费金额 (USD)' -> '{col}' (中间值: {val})")
                elif '成效' not in column_mappings and i == len(numeric_cols) - 1:
                    column_mappings['成效'] = col
                    print(f"通过数值大小猜测: '成效' -> '{col}' (最小值: {val})")
        
        # 3. 广告账户特殊处理 - 检查'Unnamed: 3'列的内容
        if '广告账户' not in column_mappings and 'Unnamed: 3' in data.columns:
            column_mappings['广告账户'] = 'Unnamed: 3'
            print("特殊处理: 使用'Unnamed: 3'列作为广告账户")
        
        # 4. 处理已花费金额特殊情况
        if '已花费金额 (USD)' not in column_mappings:
            # 如果有USD列，检查它是否是字符串列
            if 'USD' in data.columns:
                if data['USD'].dtype == 'object':
                    # 通常USD列是货币单位，不是实际金额
                    print("'USD'列可能是货币单位而非金额，继续查找...")
                else:
                    # 如果是数值，可能是金额
                    column_mappings['已花费金额 (USD)'] = 'USD'
                    print("使用'USD'列作为已花费金额")
            
            # 查找其他可能是花费的数值列
            for col in [25.43, 50.19]:
                if col in data.columns:
                    column_mappings['已花费金额 (USD)'] = col
                    print(f"使用数值列{col}作为已花费金额")
                    break
        
        # 5. 检查是否找到所有必要的列
        missing_cols = [col for col in ['广告名称', '展示次数', '点击量（全部）', '成效', '已花费金额 (USD)', '广告账户'] 
                      if col not in column_mappings]
        if missing_cols:
            print(f"警告: 未找到以下列的映射: {missing_cols}")
            
        # 打印最终的列映射结果
        print("最终列映射结果:")
        for target_col, source_col in column_mappings.items():
            print(f"  {target_col} <- {source_col}")
        
        # 定义公式列及其模板
        formula_templates = {
            '广告系列': '=VLOOKUP(E{row},Sheet2!C:E,2,0)',
            '广告组': '=VLOOKUP(E{row},Sheet2!C:E,3,0)',
            '短剧编号': '=VLOOKUP(E{row},唯一广告名称!A:B,2,0)',
            '千次展示费用 (USD)': '=IFERROR(P{row}/G{row}*1000,0)',
            '点击注册转化率': '=IFERROR(K{row}/I{row},0)',
            '展示注册转化率': '=IFERROR(K{row}/G{row},0)',
            '点击率': '=I{row}/G{row}',
            '单次注册成本': '=IFERROR(P{row}/K{row},0)'
        }
        
        # 添加数据
        rows_added = 0
        print(f"开始添加 {len(data)} 行数据...")
        
        for idx, (_, row_data) in enumerate(data.iterrows()):
            current_row = start_row + idx
            
            # 添加基础数据列
            for target_col, source_col in column_mappings.items():
                # 跳过公式列
                if target_col in formula_templates:
                    continue
                
                if target_col in column_mapping:
                    col_idx = column_mapping[target_col]
                    try:
                        value = row_data.get(source_col)
                        
                        # 特殊处理
                        if target_col == '广告账户' and isinstance(value, str):
                            value = extract_number(value)
                        elif target_col == '时间':
                            value = format_date(value)
                            
                        # 防止None值
                        if value is None:
                            print(f"警告: 行{current_row}的{target_col}列值为None，尝试再次获取")
                            # 尝试直接用索引获取
                            if isinstance(source_col, int) or (isinstance(source_col, str) and source_col.isdigit()):
                                try:
                                    col_idx_in_df = int(source_col)
                                    if col_idx_in_df < len(row_data):
                                        value = row_data.iloc[col_idx_in_df]
                                        print(f"通过索引{col_idx_in_df}获取值: {value}")
                                except:
                                    print(f"无法通过索引{source_col}获取值")
                            
                            # 如果还是None，则跳过
                            if value is None:
                                continue
                        
                        # 字符串"nan"转为空值
                        if isinstance(value, str) and value.lower() == 'nan':
                            value = None
                            
                        worksheet.cell(row=current_row, column=col_idx, value=value)
                        
                        if idx < 3:  # 打印前三行的详情
                            print(f"写入: 行={current_row}, 列={col_idx} ({target_col}), 值={value}")
                    except Exception as e:
                        print(f"写入 {target_col} 时出错: {e}")
                        print(f"行数据键: {row_data.keys() if hasattr(row_data, 'keys') else '无键方法'}")
                        print(f"尝试获取的列: {source_col}")
                        
            # 添加公式列
            for target_col, formula_template in formula_templates.items():
                if target_col in column_mapping:
                    col_idx = column_mapping[target_col]
                    try:
                        # 为第一行使用原始值
                        if idx == 0 and (target_col == '广告系列' or target_col == '广告组'):
                            source_col = column_mappings.get(target_col)
                            if source_col and source_col in row_data:
                                worksheet.cell(row=current_row, column=col_idx, value=row_data[source_col])
                                continue
                        
                        # 为当前行生成正确的公式
                        formula = formula_template.format(row=current_row)
                        
                        # 确保公式以等号开头
                        formula = ensure_formula_has_equals(formula)
                        
                        worksheet.cell(row=current_row, column=col_idx, value=formula)
                        
                        if idx < 3:  # 打印前三行详情
                            print(f"公式: 行={current_row}, 列={col_idx} ({target_col}), 公式={formula}")
                    except Exception as e:
                        print(f"写入公式 {target_col} 时出错: {e}")
                        traceback.print_exc()
                        
            rows_added += 1
            if rows_added % 10 == 0:
                print(f"已添加 {rows_added}/{len(data)} 行...")
        
        print(f"成功添加 {rows_added} 行数据")
        return rows_added
    except Exception as e:
        print(f"处理台账工作表时出错: {e}")
        traceback.print_exc()
        return 0

# 修改处理数据函数中的列映射逻辑
def process_data_for_import(data, numeric_cols=None, source_file=""):
    """
    处理数据，转换为标准格式，准备导入
    返回处理后的字典列表，每个字典代表一行数据
    """
    try:
        # 1. 创建列映射
        column_mappings = {}
        
        # 检查数据列
        print("数据列预览:")
        for col in data.columns:
            try:
                preview = data[col].iloc[0]
                print(f"列 {col}: {preview}")
            except:
                print(f"列 {col}: 无法获取预览")
        
        # 尝试通过列名映射
        column_names_to_find = {
            '广告账户': ['帐户名称', '账户名称', '账户', 'Unnamed: 3', 3],
            '时间': ['报告结束日期', '报告日期', '日期', 'Unnamed: 13', 13, '2025-07-17', '2025-07-17.1', '2025-07-31', '2025-07-31.1'],
            '广告名称': ['广告名称', '广告', 'Unnamed: 2', 2],
            '展示次数': ['展示次数', '展示量', 'Unnamed: 5', 5, 20158, 38870],
            '点击量（全部）': ['点击量（全部）', '点击量', 'Unnamed: 10', 10, 1755, 4980],
            '成效': ['网站完成注册次数', '网站注册费用', '成效', 'Unnamed: 9', 9, 58, 110, 0.43844828],
            '已花费金额 (USD)': ['已花费金额 (USD)', '已花费金额', '花费', 'Unnamed: 7', 7, 25.43, 50.19, 'USD']
        }
        
        for target_col, possible_cols in column_names_to_find.items():
            for col in possible_cols:
                if col in data.columns:
                    column_mappings[target_col] = col
                    print(f"通过列名匹配: '{target_col}' -> '{col}'")
                    break
        
        # 使用数值列信息来猜测
        if numeric_cols and len(numeric_cols) > 0:
            # 按值从大到小排序
            for i, (col, val) in enumerate(numeric_cols):
                if '展示次数' not in column_mappings and i == 0:
                    column_mappings['展示次数'] = col
                    print(f"通过数值大小猜测: '展示次数' -> '{col}' (最大值: {val})")
                elif '点击量（全部）' not in column_mappings and i == 1:
                    column_mappings['点击量（全部）'] = col
                    print(f"通过数值大小猜测: '点击量（全部）' -> '{col}' (第二大值: {val})")
                elif '已花费金额 (USD)' not in column_mappings and 1 < i < len(numeric_cols) - 1:
                    column_mappings['已花费金额 (USD)'] = col
                    print(f"通过数值大小猜测: '已花费金额 (USD)' -> '{col}' (中间值: {val})")
                elif '成效' not in column_mappings and i == len(numeric_cols) - 1:
                    column_mappings['成效'] = col
                    print(f"通过数值大小猜测: '成效' -> '{col}' (最小值: {val})")
        
        # 广告账户特殊处理 - 检查'Unnamed: 3'列的内容
        if '广告账户' not in column_mappings and 'Unnamed: 3' in data.columns:
            column_mappings['广告账户'] = 'Unnamed: 3'
            print("特殊处理: 使用'Unnamed: 3'列作为广告账户")
        
        # 处理已花费金额特殊情况
        if '已花费金额 (USD)' not in column_mappings:
            # 如果有USD列，检查它是否是字符串列
            if 'USD' in data.columns:
                if data['USD'].dtype == 'object':
                    # 通常USD列是货币单位，不是实际金额
                    print("'USD'列可能是货币单位而非金额，继续查找...")
                else:
                    # 如果是数值，可能是金额
                    column_mappings['已花费金额 (USD)'] = 'USD'
                    print("使用'USD'列作为已花费金额")
            
            # 查找其他可能是花费的数值列
            for col in [25.43, 50.19]:
                if col in data.columns:
                    column_mappings['已花费金额 (USD)'] = col
                    print(f"使用数值列{col}作为已花费金额")
                    break
        
        # 检查是否找到所有必要的列
        missing_cols = [col for col in ['广告名称', '展示次数', '点击量（全部）', '成效', '已花费金额 (USD)', '广告账户'] 
                      if col not in column_mappings]
        if missing_cols:
            print(f"警告: 未找到以下列的映射: {missing_cols}")
            
        # 打印最终的列映射结果
        print("最终列映射结果:")
        for target_col, source_col in column_mappings.items():
            print(f"  {target_col} <- {source_col}")
        
        # 2. 转换数据为标准格式
        processed_data = []
        
        for idx, (_, row_data) in enumerate(data.iterrows()):
            row_dict = {'来源文件': source_file}
            
            # 提取基础数据列
            for target_col, source_col in column_mappings.items():
                try:
                    value = row_data.get(source_col)
                    
                    # 特殊处理
                    if target_col == '广告账户' and (isinstance(value, str) or isinstance(value, (int, float))):
                        # 提取数字并转为整数，去掉前导零
                        value = extract_number(value, as_int=True)
                    elif target_col == '时间':
                        value = format_date(value)
                        
                    # 防止None值
                    if value is None:
                        print(f"警告: 行{idx+1}的{target_col}列值为None，尝试再次获取")
                        # 尝试直接用索引获取
                        if isinstance(source_col, int) or (isinstance(source_col, str) and source_col.isdigit()):
                            try:
                                col_idx_in_df = int(source_col)
                                if col_idx_in_df < len(row_data):
                                    value = row_data.iloc[col_idx_in_df]
                                    print(f"通过索引{col_idx_in_df}获取值: {value}")
                            except:
                                print(f"无法通过索引{source_col}获取值")
                    
                    # 字符串"nan"转为空值
                    if isinstance(value, str) and value.lower() == 'nan':
                        value = None
                        
                    row_dict[target_col] = value
                except Exception as e:
                    print(f"处理 {target_col} 时出错: {e}")
            
            processed_data.append(row_dict)
            
        print(f"成功处理 {len(processed_data)} 行数据")
        return processed_data
    except Exception as e:
        print(f"处理数据时出错: {e}")
        traceback.print_exc()  # 打印详细错误
        return []

def clean_currency_value(value):
    """
    清理货币数值，移除USD等货币符号
    """
    if pd.isna(value) or value is None:
        return value

    # 转换为字符串进行处理
    str_value = str(value).strip()

    # 如果已经是数值，直接返回
    try:
        return float(str_value)
    except ValueError:
        pass

    # 移除常见的货币符号和格式
    currency_symbols = ['USD', 'CNY', 'RMB', '$', '¥', '€', '£', '￥']
    for symbol in currency_symbols:
        str_value = str_value.replace(symbol, '')

    # 移除逗号分隔符
    str_value = str_value.replace(',', '')

    # 移除多余的空格
    str_value = str_value.strip()

    # 尝试转换为数值
    try:
        return float(str_value)
    except ValueError:
        return value  # 如果无法转换，返回原值

def create_record_key(row_data):
    """
    创建记录的唯一标识键
    基于：广告账户 + 时间 + 广告名称
    """
    try:
        account = str(row_data.get('广告账户', '')).strip()
        time_val = str(row_data.get('时间', '')).strip()
        ad_name = str(row_data.get('广告名称', '')).strip()

        # 标准化时间格式
        if time_val and time_val != 'nan':
            try:
                # 尝试解析并标准化日期格式
                if '/' in time_val:
                    parts = time_val.split('/')
                    if len(parts) == 3:
                        year, month, day = parts
                        time_val = f"{year}/{int(month)}/{int(day)}"
            except:
                pass

        key = f"{account}|{time_val}|{ad_name}"
        return key
    except Exception as e:
        print(f"创建记录键时出错: {e}")
        return ""

def read_existing_data(worksheet):
    """
    读取工作表中的现有数据，创建索引
    返回：{record_key: row_number} 的字典
    """
    existing_data = {}

    try:
        # 获取表头信息
        headers = {}
        for col in range(1, worksheet.max_column + 1):
            header = worksheet.cell(row=1, column=col).value
            if header:
                headers[header] = col

        print(f"读取现有数据，表头: {list(headers.keys())}")

        # 读取数据行
        for row in range(2, worksheet.max_row + 1):
            row_data = {}

            # 读取关键字段
            for field in ['广告账户', '时间', '广告名称']:
                if field in headers:
                    col_idx = headers[field]
                    cell_value = worksheet.cell(row=row, column=col_idx).value
                    row_data[field] = cell_value

            # 创建记录键
            record_key = create_record_key(row_data)
            if record_key and record_key != "||":  # 确保不是空键
                existing_data[record_key] = row

        print(f"读取到 {len(existing_data)} 条现有记录")
        return existing_data

    except Exception as e:
        print(f"读取现有数据时出错: {e}")
        traceback.print_exc()
        return {}

def clean_worksheet_empty_rows(worksheet, max_check_rows=10000):
    """
    清理工作表中的空行，释放空间
    只检查最后的max_check_rows行，避免影响有数据的行
    """
    try:
        print("开始清理工作表空行...")

        # 找到真正的最后一行数据
        actual_last_row = find_last_data_row(worksheet)
        max_row = worksheet.max_row

        print(f"实际数据最后一行: {actual_last_row}, 工作表max_row: {max_row}")

        if max_row > actual_last_row + 100:  # 如果有超过100行的空行
            # 删除空行（从后往前删除）
            rows_to_delete = max_row - actual_last_row
            if rows_to_delete > max_check_rows:
                rows_to_delete = max_check_rows

            print(f"准备删除 {rows_to_delete} 行空行...")

            # 删除空行
            for i in range(rows_to_delete):
                worksheet.delete_rows(max_row - i)

            print(f"已删除 {rows_to_delete} 行空行")
            return rows_to_delete
        else:
            print("没有发现需要清理的空行")
            return 0

    except Exception as e:
        print(f"清理工作表空行时出错: {e}")
        return 0

def merge_data_to_sheet(workbook, data_list, sheet_name, start_row):
    """
    智能合并数据到工作表：相同记录更新数值，新记录添加
    替代原来的 append_data_to_sheet 函数
    """
    if not data_list:
        print("没有数据需要合并")
        return 0

    try:
        print(f"开始智能合并数据到工作表 '{sheet_name}'...")

        worksheet = workbook[sheet_name]

        # 获取表头信息
        headers = {}
        column_mapping = {}
        for col in range(1, worksheet.max_column + 1):
            header = worksheet.cell(row=1, column=col).value
            if header:
                headers[header] = col
                column_mapping[header] = col

        print(f"表头: {list(headers.keys())}")

        # 读取现有数据索引
        existing_data = read_existing_data(worksheet)

        # 定义数值字段（需要更新的字段）
        numeric_fields = ['展示次数', '点击量（全部）', '成效', '已花费金额 (USD)']

        # 定义公式列及其模板
        formula_templates = {
            '广告系列': '=VLOOKUP(E{row},Sheet2!C:E,2,0)',
            '广告组': '=VLOOKUP(E{row},Sheet2!C:E,3,0)',
            '短剧编号': '=VLOOKUP(E{row},唯一广告名称!A:B,2,0)',
            '千次展示费用 (USD)': '=IFERROR(P{row}/G{row}*1000,0)',
            '点击注册转化率': '=IFERROR(K{row}/I{row},0)',
            '展示注册转化率': '=IFERROR(K{row}/G{row},0)',
            '点击率': '=I{row}/G{row}',
            '单次注册成本': '=IFERROR(P{row}/K{row},0)'
        }

        # 统计信息
        rows_updated = 0
        rows_added = 0

        print(f"开始处理 {len(data_list)} 条数据...")

        for idx, row_data in enumerate(data_list):
            # 创建记录键
            record_key = create_record_key(row_data)

            if record_key in existing_data:
                # 记录已存在，更新数值字段
                existing_row = existing_data[record_key]
                print(f"更新现有记录 (行 {existing_row}): {record_key}")

                # 只更新数值字段
                for field in numeric_fields:
                    if field in row_data and field in column_mapping:
                        col_idx = column_mapping[field]
                        value = row_data[field]
                        if value is not None:
                            # 清理货币格式
                            if field == '已花费金额 (USD)':
                                value = clean_currency_value(value)
                            worksheet.cell(row=existing_row, column=col_idx, value=value)
                            if idx < 3:  # 打印前三行详情
                                print(f"  更新: 列={col_idx} ({field}), 值={value}")

                # 重新计算公式列（通过重新设置公式来触发计算）
                for target_col, formula_template in formula_templates.items():
                    if target_col in column_mapping:
                        col_idx = column_mapping[target_col]
                        formula = formula_template.format(row=existing_row)
                        formula = ensure_formula_has_equals(formula)
                        worksheet.cell(row=existing_row, column=col_idx, value=formula)

                rows_updated += 1
            else:
                # 新记录，添加到末尾
                # 使用find_last_data_row来找到真正的最后一行，避免Excel行数限制问题
                actual_last_row = find_last_data_row(worksheet)
                current_row = actual_last_row + 1

                # 检查是否超出Excel行数限制
                if current_row > 1048576:
                    print(f"警告：工作表已达到Excel最大行数限制，无法添加更多数据")
                    print(f"当前尝试写入行号：{current_row}，最大允许：1048576")
                    break

                print(f"添加新记录 (行 {current_row}): {record_key}")

                # 添加基础数据列
                for target_col, value in row_data.items():
                    # 跳过公式列和内部使用列
                    if target_col in formula_templates or target_col in ['来源文件', '广告账户_排序']:
                        continue

                    if target_col in column_mapping:
                        col_idx = column_mapping[target_col]
                        try:
                            # 清理货币格式
                            if target_col == '已花费金额 (USD)':
                                value = clean_currency_value(value)
                            worksheet.cell(row=current_row, column=col_idx, value=value)

                            if idx < 3:  # 打印前三行的详情
                                print(f"  写入: 列={col_idx} ({target_col}), 值={value}")
                        except Exception as e:
                            print(f"写入 {target_col} 时出错: {e}")

                # 添加公式列
                for target_col, formula_template in formula_templates.items():
                    if target_col in column_mapping:
                        col_idx = column_mapping[target_col]
                        try:
                            # 为当前行生成正确的公式
                            formula = formula_template.format(row=current_row)
                            formula = ensure_formula_has_equals(formula)
                            worksheet.cell(row=current_row, column=col_idx, value=formula)

                            if idx < 3:  # 打印前三行详情
                                print(f"  公式: 列={col_idx} ({target_col}), 公式={formula}")
                        except Exception as e:
                            print(f"写入公式 {target_col} 时出错: {e}")

                # 更新现有数据索引
                existing_data[record_key] = current_row
                rows_added += 1

            if (idx + 1) % 10 == 0:
                print(f"已处理 {idx + 1}/{len(data_list)} 条数据...")

        print(f"合并完成: 更新了 {rows_updated} 行，新增了 {rows_added} 行")
        return rows_added + rows_updated

    except Exception as e:
        print(f"合并数据到工作表时出错: {e}")
        traceback.print_exc()
        return 0

def append_data_to_sheet(workbook, data_list, sheet_name, start_row):
    """
    保留原函数以兼容性，但内部调用新的合并函数
    """
    return merge_data_to_sheet(workbook, data_list, sheet_name, start_row)

if __name__ == "__main__":
    # 源数据目录
    source_dir = r'C:\Users\<USER>\Desktop\juadge_data_deal\Data\origin_data'
    # 目标Excel文件
    target_excel = r'C:\Users\<USER>\Desktop\juadge_data_deal\Data\deal_data\广告数据对比总表.xlsx'
    
    try:
        # 处理文件
        print("开始处理文件...")
        process_files_to_excel(source_dir, target_excel)
        print("处理完成")
    except Exception as e:
        print(f"脚本执行时出错: {e}")
        traceback.print_exc()  # 打印详细错误 